package com.fytec.entity.external;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.annotation.DictData;
import com.fytec.entity.BaseSnowIdEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 能力服务每日使用统计表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName("external_billing_daily_stats")
@Schema(description = "能力服务每日使用统计表")
public class ExternalBillingDailyStats extends BaseSnowIdEntity {

    /**
     * 能力服务编码（对应logType）
     */
    @Schema(description = "能力服务编码（对应logType）")
    @TableField("service_code")
    private String serviceCode;

    /**
     * 统计日期
     */
    @Schema(description = "统计日期")
    @TableField("stats_date")
    private LocalDate statsDate;

    /**
     * 服务次数（当日调用总次数）
     */
    @Schema(description = "服务次数（当日调用总次数）")
    @TableField("service_count")
    private Long serviceCount;

    /**
     * 使用时长（分钟）-仅语音服务
     */
    @Schema(description = "使用时长（分钟）-仅语音服务")
    @TableField("usage_duration_minutes")
    private Long usageDurationMinutes;

    /**
     * 服务字数-仅文字识别服务
     */
    @Schema(description = "服务字数-仅文字识别服务")
    @TableField("service_word_count")
    private Long serviceWordCount;

    /**
     * 使用次数总计
     */
    @Schema(description = "使用次数总计")
    @TableField("use_count")
    private Long useCount;

    /**
     * 创建者 - 重写父类字段以匹配数据库bigint类型
     */
    @Schema(description = "创建者")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 更新者 - 重写父类字段以匹配数据库bigint类型
     */
    @Schema(description = "更新者")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;
}
